#all-maps {
  height: 100%;
  width: 100%;
  z-index: 1;
}

// Map Controls (right side buttons)
.map-controls {
  position: absolute;
  top: 95px;
  right: 15px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-btn {
  --background: rgba(255, 255, 255, 0.95);
  --color: var(--ion-color-primary);
  --border-radius: 12px;
  width: 48px;
  height: 48px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);

  .control-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }
}

// All Centers Panel (slides from right)
.all-centers-panel {
  position: fixed;
  top: 0;
  right: -100%;
  width: 350px;
  height: 100vh;
  background: white;
  z-index: 2000;
  transition: right 0.3s ease-in-out;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);

  &.show {
    right: 0;
  }

  .panel-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    padding-top: 60px; // Account for status bar
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--ion-color-light);

    .header-info {
      flex: 1;

      h3 {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--ion-color-secondary);
        line-height: 1.2;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: var(--ion-color-medium);
        line-height: 1.3;
      }
    }

    ion-button {
      --color: var(--ion-color-medium);
      margin: 0;
    }
  }

  .disaster-counts {
    margin-bottom: 20px;
    padding: 16px;
    background: var(--ion-color-light);
    border-radius: 12px;

    .count-row {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 6px 0;
      font-size: 14px;

      .disaster-icon {
        font-size: 16px;
        width: 20px;
        text-align: center;
      }

      .disaster-label {
        flex: 1;
        color: var(--ion-color-dark);
        font-weight: 500;
      }

      .disaster-count {
        font-weight: 600;
        color: var(--ion-color-secondary);
        min-width: 24px;
        text-align: right;
      }
    }
  }

  .centers-list {
    flex: 1;
    overflow-y: auto;

    .center-item {
      display: flex;
      align-items: center;
      padding: 16px;
      margin-bottom: 8px;
      background: white;
      border: 1px solid var(--ion-color-light);
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--ion-color-light);
        border-color: var(--ion-color-primary-tint);
      }

      .center-info {
        flex: 1;

        h4 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--ion-color-dark);
          line-height: 1.2;
        }

        .address {
          margin: 0 0 8px 0;
          font-size: 13px;
          color: var(--ion-color-medium);
          line-height: 1.3;
        }

        .center-details {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .distance, .capacity {
            font-size: 12px;
            color: var(--ion-color-medium);
          }

          .distance {
            color: var(--ion-color-primary);
            font-weight: 500;
          }
        }
      }

      .center-actions {
        ion-icon {
          font-size: 20px;
          color: var(--ion-color-medium);
        }
      }
    }
  }
}

// Overlay to close all centers panel
.all-centers-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.show {
    opacity: 1;
    visibility: visible;
  }
}

// All maps styling
ion-toolbar {
  --background: var(--ion-color-secondary);
  --color: white;
}

ion-title {
  font-weight: 600;
}

// Transportation Controls
.transport-controls {
  position: absolute;
  bottom: 120px;
  left: 20px;
  z-index: 1000;
  max-width: 280px;

  ion-card {
    margin: 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  ion-card-content {
    padding: 12px;
  }

  .transport-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--ion-color-primary);
    margin-bottom: 8px;

    ion-icon {
      font-size: 18px;
    }
  }

  ion-segment {
    --background: rgba(var(--ion-color-light-rgb), 0.3);
    border-radius: 8px;
  }

  ion-segment-button {
    --color: var(--ion-color-medium);
    --color-checked: var(--ion-color-primary);
    --indicator-color: var(--ion-color-primary);
    min-height: 40px;

    ion-icon {
      font-size: 16px;
      margin-bottom: 2px;
    }

    ion-label {
      font-size: 12px;
      font-weight: 500;
    }
  }
}



// Pulsing Marker Animation
:global(.pulsing-marker) {
  .pulse-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .pulse {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    opacity: 0.6;
    animation: pulse 2s infinite;
    z-index: 1;
  }

  .marker-icon {
    width: 40px;
    height: 40px;
    z-index: 2;
    position: relative;
  }

  .marker-label {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--ion-color-primary);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    z-index: 3;
    border: 2px solid white;
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.4;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}

// Navigation Panel (slides from right)
.navigation-panel {
  position: fixed;
  top: 0;
  right: -100%;
  width: 320px;
  height: 100vh;
  background: white;
  z-index: 2000;
  transition: right 0.3s ease-in-out;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);

  &.show {
    right: 0;
  }

  .panel-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    padding-top: 60px; // Account for status bar
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--ion-color-light);

    .center-info {
      flex: 1;

      h3 {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--ion-color-dark);
        line-height: 1.2;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: var(--ion-color-medium);
        line-height: 1.3;
      }
    }

    ion-button {
      --color: var(--ion-color-medium);
      margin: 0;
    }
  }

  .transport-options {
    flex: 1;

    .option-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      font-weight: 600;
      color: var(--ion-color-primary);

      ion-icon {
        font-size: 20px;
      }
    }

    .transport-buttons {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 24px;
    }

    .transport-btn {
      display: flex;
      align-items: center;
      padding: 16px;
      border: 2px solid var(--ion-color-light);
      border-radius: 12px;
      background: white;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        border-color: var(--ion-color-primary-tint);
        background: var(--ion-color-primary-tint);
      }

      &.active {
        border-color: var(--ion-color-primary);
        background: var(--ion-color-primary-tint);

        ion-icon {
          color: var(--ion-color-primary);
        }
      }

      ion-icon {
        font-size: 24px;
        margin-right: 12px;
        color: var(--ion-color-medium);
        transition: color 0.2s ease;
      }

      > span {
        font-size: 16px;
        font-weight: 500;
        color: var(--ion-color-dark);
        flex: 1;
      }

      .route-info {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 2px;

        .time {
          font-size: 16px;
          font-weight: 600;
          color: var(--ion-color-primary);
        }

        .distance {
          font-size: 12px;
          color: var(--ion-color-medium);
        }
      }
    }

    .start-navigation-btn {
      width: 100%;
      padding: 16px;
      background: var(--ion-color-primary);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      cursor: pointer;
      transition: background 0.2s ease;

      &:hover {
        background: var(--ion-color-primary-shade);
      }

      ion-icon {
        font-size: 20px;
      }
    }
  }
}

// Popup styling for all centers
:global(.leaflet-popup-content) {
  .evacuation-popup {
    text-align: center;
    min-width: 200px;

    h3 {
      margin: 0 0 8px 0;
      color: var(--ion-color-secondary);
      font-size: 16px;
      font-weight: 600;
    }

    h4 {
      margin: 4px 0;
      color: var(--ion-color-dark);
      font-size: 14px;
      font-weight: 500;
    }

    p {
      margin: 4px 0;
      font-size: 14px;

      strong {
        color: var(--ion-color-dark);
      }
    }

    &.nearest-popup {
      h3 {
        color: var(--ion-color-success);
        font-size: 18px;
      }
    }
  }
}
