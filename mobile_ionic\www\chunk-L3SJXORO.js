import{a as mt}from"./chunk-2HTZH2KL.js";import{a as ut,c as _t}from"./chunk-GX6AP5HP.js";import{a as ht}from"./chunk-FN57SI3Q.js";import{b as gt}from"./chunk-I4SN7ED3.js";import{a as pt}from"./chunk-IEIMMQCN.js";import"./chunk-3J7GGTVR.js";import{a as dt}from"./chunk-WPPT3EJF.js";import"./chunk-2LL5MXLB.js";import{B as s,Db as Z,F as v,G as _,Hb as tt,I as w,Ib as et,K as i,L as a,Lb as nt,M as u,P as k,Q as C,R as h,Rb as ot,Sb as it,Y as g,Yb as at,Z as M,_ as L,aa as A,ac as rt,ba as R,ca as $,cb as j,cc as st,d as D,da as z,ec as lt,fb as J,fc as ct,ib as K,l as x,m as E,ma as F,na as V,pa as B,r as b,ra as W,s as y,sb as q,vb as H,wa as G,wb as Y,xb as Q,ya as U,yb as X}from"./chunk-E442IOFQ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{a as S,b as I,f as ft,g as m}from"./chunk-2R6CW7ES.js";var c=ft(_t());function Ct(l,P){if(l&1&&(i(0,"div",42)(1,"ion-card")(2,"ion-card-content")(3,"div",43),u(4,"ion-icon",44),i(5,"span"),g(6,"Route to Nearest Center"),a()(),i(7,"div",38)(8,"div",45),u(9,"ion-icon",37),i(10,"span"),g(11),a()(),i(12,"div",45),u(13,"ion-icon",46),i(14,"span"),g(15),a()()()()()()),l&2){let t=h();s(9),_("name",t.travelMode==="walking"?"walk-outline":t.travelMode==="cycling"?"bicycle-outline":"car-outline"),s(2),L("",(t.routeTime/60).toFixed(0)," min"),s(4),L("",(t.routeDistance/1e3).toFixed(2)," km")}}function Mt(l,P){if(l&1&&(i(0,"span",55),g(1),a()),l&2){let t=h().$implicit,n=h();s(),L(" \u{1F4CD} ",n.calculateDistanceInKm(t)," km away ")}}function vt(l,P){if(l&1){let t=k();i(0,"div",47),C("click",function(){let e=b(t).$implicit,o=h();return y(o.selectCenterFromList(e))}),i(1,"div",48)(2,"h4"),g(3),a(),i(4,"p",49),g(5),a(),i(6,"div",50),v(7,Mt,2,1,"span",51),i(8,"span",52),g(9),a()()(),i(10,"div",53),u(11,"ion-icon",54),a()()}if(l&2){let t=P.$implicit,n=h();s(3),M(t.name),s(2),M(t.address),s(2),_("ngIf",n.userLocation),s(2),L("\u{1F465} ",t.capacity||"N/A"," capacity")}}function Pt(l,P){if(l&1){let t=k();i(0,"app-real-time-navigation",56),C("routeUpdated",function(e){b(t);let o=h();return y(o.onNavigationRouteUpdated(e))})("navigationStopped",function(){b(t);let e=h();return y(e.onNavigationStopped())}),a()}if(l&2){let t=h();_("destination",t.navigationDestination)("travelMode",t.selectedTransportMode==="walking"?"foot-walking":t.selectedTransportMode==="cycling"?"cycling-regular":"driving-car")("autoStart",t.isRealTimeNavigationActive)}}function xt(l,P){if(l&1&&(i(0,"p"),g(1),a()),l&2){let t=h();s(),M(t.selectedCenter.address)}}function Ot(l,P){if(l&1&&(i(0,"div",42)(1,"span",63),g(2),a(),i(3,"span",55),g(4),a()()),l&2){let t=h(2);s(2),M(t.formatTime(t.routeInfo.walking.duration)),s(2),M(t.formatDistance(t.routeInfo.walking.distance))}}function bt(l,P){if(l&1&&(i(0,"div",42)(1,"span",63),g(2),a(),i(3,"span",55),g(4),a()()),l&2){let t=h(2);s(2),M(t.formatTime(t.routeInfo.cycling.duration)),s(2),M(t.formatDistance(t.routeInfo.cycling.distance))}}function yt(l,P){if(l&1&&(i(0,"div",42)(1,"span",63),g(2),a(),i(3,"span",55),g(4),a()()),l&2){let t=h(2);s(2),M(t.formatTime(t.routeInfo.driving.duration)),s(2),M(t.formatDistance(t.routeInfo.driving.distance))}}function wt(l,P){if(l&1){let t=k();i(0,"button",64),C("click",function(){b(t);let e=h(2);return y(e.startRealTimeNavigation(e.selectedCenter))}),u(1,"ion-icon",59),g(2," Start Navigation "),a()}}function Lt(l,P){if(l&1){let t=k();i(0,"div",57)(1,"div",58),u(2,"ion-icon",59),i(3,"span"),g(4,"Choose Transportation"),a()(),i(5,"div",60)(6,"button",61),C("click",function(){b(t);let e=h();return y(e.selectTransportMode("walking"))}),u(7,"ion-icon",15),i(8,"span"),g(9,"Walk"),a(),v(10,Ot,5,2,"div",20),a(),i(11,"button",61),C("click",function(){b(t);let e=h();return y(e.selectTransportMode("cycling"))}),u(12,"ion-icon",17),i(13,"span"),g(14,"Bike"),a(),v(15,bt,5,2,"div",20),a(),i(16,"button",61),C("click",function(){b(t);let e=h();return y(e.selectTransportMode("driving"))}),u(17,"ion-icon",19),i(18,"span"),g(19,"Drive"),a(),v(20,yt,5,2,"div",20),a()(),v(21,wt,3,0,"button",62),a()}if(l&2){let t=h();s(6),w("active",t.selectedTransportMode==="walking"),s(4),_("ngIf",t.routeInfo.walking),s(),w("active",t.selectedTransportMode==="cycling"),s(4),_("ngIf",t.routeInfo.cycling),s(),w("active",t.selectedTransportMode==="driving"),s(4),_("ngIf",t.routeInfo.driving),s(),_("ngIf",t.selectedTransportMode)}}function Nt(l,P){if(l&1&&(i(0,"div",65)(1,"span",63),g(2),a(),i(3,"span",55),g(4),a()()),l&2){let t=h();s(2),M(t.formatTime(t.routeInfo[t.selectedTransportMode]==null?null:t.routeInfo[t.selectedTransportMode].duration)),s(2),M(t.formatDistance(t.routeInfo[t.selectedTransportMode]==null?null:t.routeInfo[t.selectedTransportMode].distance))}}var Kt=(()=>{class l{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.userLocation=null,this.newCenterId=null,this.highlightCenter=!1,this.centerLat=null,this.centerLng=null,this.showAllCentersPanel=!1,this.showRouteFooter=!1,this.travelMode="walking",this.routeTime=0,this.routeDistance=0,this.selectedCenter=null,this.selectedTransportMode="walking",this.routeInfo={},this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.loadingCtrl=x(st),this.toastCtrl=x(lt),this.alertCtrl=x(rt),this.http=x(W),this.router=x(U),this.route=x(G),this.osmRouting=x(pt),this.mapboxRouting=x(ut),this.enhancedDownload=x(mt)}ngOnInit(){console.log("\u{1F3D4}\uFE0F LANDSLIDE MAP: Component initialized..."),this.route.queryParams.subscribe(t=>{t.newCenterId&&(this.newCenterId=t.newCenterId,this.highlightCenter=t.highlightCenter==="true",this.centerLat=t.centerLat?parseFloat(t.centerLat):null,this.centerLng=t.centerLng?parseFloat(t.centerLng):null,console.log("\u{1F3D4}\uFE0F LANDSLIDE MAP: New center to highlight:",this.newCenterId))})}ngAfterViewInit(){return m(this,null,function*(){console.log("\u{1F3D4}\uFE0F LANDSLIDE MAP: View initialized, loading map..."),setTimeout(()=>m(this,null,function*(){yield this.loadLandslideMap()}),100)})}loadLandslideMap(){return m(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading landslide evacuation centers...",spinner:"crescent"});yield t.present();try{let n=yield gt.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),e=n.coords.latitude,o=n.coords.longitude;this.userLocation={lat:e,lng:o},console.log(`\u{1F3D4}\uFE0F LANDSLIDE MAP: User location [${e}, ${o}]`),this.initializeMap(e,o),yield this.loadLandslideCenters(e,o),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F3D4}\uFE0F Showing ${this.evacuationCenters.length} landslide evacuation centers`,duration:3e3,color:"tertiary",position:"top"})).present()}catch(n){yield t.dismiss(),console.error("\u{1F3D4}\uFE0F LANDSLIDE MAP: Error loading map",n),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadLandslideMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,n){if(console.log(`\u{1F3D4}\uFE0F LANDSLIDE MAP: Initializing map at [${t}, ${n}]`),!document.getElementById("landslide-map"))throw console.error("\u{1F3D4}\uFE0F LANDSLIDE MAP: Container #landslide-map not found!"),new Error("Map container not found. Please ensure the view is properly loaded.");this.map&&this.map.remove(),this.map=c.map("landslide-map").setView([t,n],13),c.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=c.marker([t,n],{icon:c.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadLandslideCenters(t,n){return m(this,null,function*(){try{console.log("\u{1F3D4}\uFE0F LANDSLIDE MAP: Fetching landslide centers...");let e=[];try{e=(yield D(this.http.get(`${dt.apiUrl}/evacuation-centers`))).data||[],console.log("\u{1F3D4}\uFE0F LANDSLIDE MAP: Total centers received from API:",e?.length||0)}catch(o){console.error("\u274C API failed:",o),yield(yield this.alertCtrl.create({header:"Connection Error",message:"Cannot connect to server. Please check your internet connection.",buttons:["OK"]})).present();return}if(this.evacuationCenters=e.filter(o=>Array.isArray(o.disaster_type)?o.disaster_type.some(r=>r==="Landslide"):o.disaster_type==="Landslide"),console.log(`\u{1F3D4}\uFE0F LANDSLIDE MAP: Filtered to ${this.evacuationCenters.length} landslide centers`),console.log("\u{1F3D4}\uFE0F LANDSLIDE MAP: Filtered centers:",this.evacuationCenters.map(o=>`${o.name} (${JSON.stringify(o.disaster_type)})`)),console.log(`\u{1F3D4}\uFE0F LANDSLIDE MAP: Filtered to ${this.evacuationCenters.length} landslide centers`),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Landslide Centers",message:"No landslide evacuation centers found in the data.",buttons:["OK"]})).present();return}yield this.addMarkersAndRoutes(t,n)}catch(e){console.error("\u{1F3D4}\uFE0F LANDSLIDE MAP: Error loading centers",e),yield(yield this.toastCtrl.create({message:"Error loading landslide centers. Please check your internet connection.",duration:4e3,color:"danger"})).present()}})}addMarkersAndRoutes(t,n){return m(this,null,function*(){if(this.evacuationCenters.forEach(e=>{let o=Number(e.latitude),r=Number(e.longitude);if(!isNaN(o)&&!isNaN(r)){let d=c.marker([o,r],{icon:c.icon({iconUrl:"assets/forLandslide.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),p=this.calculateDistance(t,n,o,r);d.on("click",()=>{console.log("\u{1F3D4}\uFE0F LANDSLIDE: Marker clicked for center:",e.name),this.showNavigationPanel(e)});let f=this.newCenterId&&e.id.toString()===this.newCenterId;d.bindPopup(`
          <div class="evacuation-popup">
            <h3>\u{1F3D4}\uFE0F ${e.name} ${f?"\u2B50 NEW!":""}</h3>
            <p><strong>Type:</strong> Landslide Center</p>
            <p><strong>Distance:</strong> ${(p/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${e.capacity||"N/A"}</p>
            <p><em>Click marker for route options</em></p>
            ${f?"<p><strong>\u{1F195} Recently Added!</strong></p>":""}
          </div>
        `),f&&(d.openPopup(),this.map.setView([o,r],15),this.toastCtrl.create({message:`\u{1F195} New landslide evacuation center: ${e.name}`,duration:5e3,color:"tertiary",position:"top"}).then(O=>O.present())),d.addTo(this.map),console.log(`\u{1F3D4}\uFE0F Added landslide marker: ${e.name}`)}}),console.log("\u{1F3D4}\uFE0F Showing simple markers without auto-routing..."),this.evacuationCenters.length>0){let e=c.latLngBounds([]);e.extend([t,n]),this.evacuationCenters.forEach(o=>{e.extend([Number(o.latitude),Number(o.longitude)])}),this.map.fitBounds(e,{padding:[50,50]})}})}calculateDistance(t,n,e,o){let d=t*Math.PI/180,p=e*Math.PI/180,f=(e-t)*Math.PI/180,O=(o-n)*Math.PI/180,N=Math.sin(f/2)*Math.sin(f/2)+Math.cos(d)*Math.cos(p)*Math.sin(O/2)*Math.sin(O/2);return 6371e3*(2*Math.atan2(Math.sqrt(N),Math.sqrt(1-N)))}routeToTwoNearestCenters(){return m(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F3D4}\uFE0F LANDSLIDE MAP: No user location or evacuation centers available");return}try{console.log("\u{1F3D4}\uFE0F LANDSLIDE MAP: Finding 2 nearest landslide centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0){yield(yield this.toastCtrl.create({message:"No landslide evacuation centers found nearby",duration:3e3,color:"warning"})).present();return}this.clearRoutes(),this.addPulsingMarkers(t),yield this.calculateRoutes(t),yield(yield this.toastCtrl.create({message:`\u{1F3D4}\uFE0F Showing routes to ${t.length} nearest landslide centers`,duration:4e3,color:"tertiary"})).present()}catch(t){console.error("\u{1F3D4}\uFE0F LANDSLIDE MAP: Error calculating routes",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}getTwoNearestCenters(t,n){return this.evacuationCenters.map(o=>I(S({},o),{distance:this.calculateDistance(t,n,Number(o.latitude),Number(o.longitude))})).sort((o,r)=>o.distance-r.distance).slice(0,2)}addPulsingMarkers(t){this.nearestMarkers.forEach(n=>this.map.removeLayer(n)),this.nearestMarkers=[],t.forEach((n,e)=>{let o=Number(n.latitude),r=Number(n.longitude);if(!isNaN(o)&&!isNaN(r)){let d=c.divIcon({className:"pulsing-marker",html:`
            <div class="pulse-container">
              <div class="pulse" style="background-color: #8B4513"></div>
              <img src="assets/forLandslide.png" class="marker-icon" />
              <div class="marker-label">${e+1}</div>
            </div>
          `,iconSize:[50,50],iconAnchor:[25,50]}),p=c.marker([o,r],{icon:d});p.bindPopup(`
          <div class="evacuation-popup nearest-popup">
            <h3>\u{1F3AF} Nearest Center #${e+1}</h3>
            <h4>${n.name}</h4>
            <p><strong>Type:</strong> Landslide</p>
            <p><strong>Distance:</strong> ${(n.distance/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${n.capacity||"N/A"}</p>
          </div>
        `),p.addTo(this.map),this.nearestMarkers.push(p)}})}calculateRoutes(t){return m(this,null,function*(){if(this.userLocation){this.routeLayer=c.layerGroup().addTo(this.map);for(let n=0;n<t.length;n++){let e=t[n],o=Number(e.latitude),r=Number(e.longitude);if(!isNaN(o)&&!isNaN(r))try{console.log(`\u{1F3D4}\uFE0F LANDSLIDE MAP: Creating Mapbox route to center ${n+1}: ${e.name}`);let d=this.mapboxRouting.convertTravelModeToProfile(this.travelMode),p=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,r,o,d);if(p&&p.routes&&p.routes.length>0){let f=p.routes[0];c.polyline(f.geometry.coordinates.map(N=>[N[1],N[0]]),{color:"#8B4513",weight:4,opacity:.8,dashArray:n===0?void 0:"10, 10"}).addTo(this.routeLayer),n===0&&(this.routeTime=f.duration,this.routeDistance=f.distance),console.log(`\u2705 LANDSLIDE MAP: Added Mapbox route to ${e.name} (${(f.distance/1e3).toFixed(2)}km, ${(f.duration/60).toFixed(0)}min)`)}}catch(d){console.error(`\u{1F3D4}\uFE0F Error calculating Mapbox route to center ${n+1}:`,d)}}}})}clearRoutes(){this.routeLayer&&(this.map.removeLayer(this.routeLayer),this.routeLayer=null),this.nearestMarkers.forEach(t=>{this.map.removeLayer(t)}),this.nearestMarkers=[],this.map.eachLayer(t=>{(t instanceof c.GeoJSON||t instanceof c.Polyline||t.options&&(t.options.color==="#8B4513"||t.options.color==="#8b5a2b"||t.isRouteLayer||t.isNavigationRoute))&&this.map.removeLayer(t)})}showOfflineMarkerInfo(t,n){return m(this,null,function*(){yield(yield this.alertCtrl.create({header:`\u{1F4F1} ${t.name}`,message:`
        <div style="text-align: left;">
          <p><strong>Type:</strong> Landslide Center</p>
          <p><strong>Distance:</strong> ${(n/1e3).toFixed(2)} km</p>
          <p><strong>Capacity:</strong> ${t.capacity||"N/A"}</p>
          <p><strong>Address:</strong> ${t.address||"N/A"}</p>
          <br>
          <p><em>\u{1F4F1} Offline Mode: Navigation features are limited</em></p>
        </div>
      `,buttons:["OK"]})).present()})}showNavigationPanel(t){return m(this,null,function*(){console.log("\u{1F3D4}\uFE0F LANDSLIDE: showNavigationPanel called for:",t.name),console.log("\u{1F3D4}\uFE0F LANDSLIDE: Setting selectedCenter to:",t),this.selectedCenter=t,this.selectedTransportMode="walking",this.routeInfo={},console.log("\u{1F3D4}\uFE0F LANDSLIDE: selectedCenter is now:",this.selectedCenter),yield this.calculateAllRoutes(t)})}calculateAllRoutes(t){return m(this,null,function*(){if(!this.userLocation)return;let n=["walking","cycling","driving"];for(let e of n)try{let o=this.mapboxRouting.convertTravelModeToProfile(e),r=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),o);if(r&&r.routes&&r.routes.length>0){let d=r.routes[0];this.routeInfo[e]={duration:d.duration,distance:d.distance}}}catch(o){console.error(`\u{1F3D4}\uFE0F Error calculating ${e} route:`,o)}})}navigateWithMode(t){return m(this,null,function*(){if(!(!this.selectedCenter||!this.userLocation)){this.selectedTransportMode=t,this.clearRoutes();try{let n=this.mapboxRouting.convertTravelModeToProfile(t),e=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(this.selectedCenter.longitude),Number(this.selectedCenter.latitude),n);if(e&&e.routes&&e.routes.length>0){let o=e.routes[0],r="#8B4513";this.routeLayer=c.layerGroup().addTo(this.map);let d=c.polyline(o.geometry.coordinates.map(f=>[f[1],f[0]]),{color:r,weight:5,opacity:.8});d.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`\u{1F3D4}\uFE0F Route: ${(o.distance/1e3).toFixed(2)}km, ${(o.duration/60).toFixed(0)}min via ${t}`,duration:4e3,color:"tertiary"})).present(),this.map.fitBounds(d.getBounds(),{padding:[50,50]})}}catch(n){console.error("\u{1F3D4}\uFE0F Error showing route:",n),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}}})}goBack(){this.router.navigate(["/tabs/home"])}onTravelModeChange(t){let n=t.detail.value;this.changeTravelMode(n)}changeTravelMode(t){return m(this,null,function*(){this.travelMode=t,yield(yield this.toastCtrl.create({message:`\u{1F3D4}\uFE0F Travel mode changed to ${t}`,duration:2e3,color:"tertiary"})).present(),this.userLocation&&this.evacuationCenters.length>0&&(yield this.routeToTwoNearestCenters())})}downloadMap(){return m(this,null,function*(){if(!this.map){yield(yield this.toastCtrl.create({message:"Map not loaded yet. Please wait and try again.",duration:3e3,color:"warning"})).present();return}try{yield this.enhancedDownload.downloadMapWithRoutes("landslide-map",this.map,"Landslide",!0)}catch(t){console.error("Enhanced download error:",t),yield(yield this.toastCtrl.create({message:"Failed to download map. Please try again.",duration:3e3,color:"danger"})).present()}})}showAllCenters(){this.showAllCentersPanel=!0}closeAllCentersPanel(){this.showAllCentersPanel=!1}routeToNearestCenters(){return m(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){yield(yield this.toastCtrl.create({message:"Unable to calculate routes. Please ensure location is available.",duration:3e3,color:"warning"})).present();return}try{this.clearRoutes(),yield this.routeToTwoNearestCenters(),yield(yield this.toastCtrl.create({message:"\u{1F3D4}\uFE0F Routes calculated to 2 nearest landslide evacuation centers",duration:4e3,color:"tertiary"})).present()}catch(t){console.error("Error calculating routes:",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}selectCenterFromList(t){this.closeAllCentersPanel(),this.openNavigationPanel(t)}calculateDistanceInKm(t){return this.userLocation?(this.calculateDistance(this.userLocation.lat,this.userLocation.lng,Number(t.latitude),Number(t.longitude))/1e3).toFixed(1):"N/A"}openNavigationPanel(t){this.selectedCenter=t,this.showRouteFooter=!0,this.calculateRouteInfo(t)}closeNavigationPanel(){this.selectedCenter=null,this.showRouteFooter=!1,this.routeInfo={}}selectTransportMode(t){this.selectedTransportMode=t,this.selectedCenter&&this.showRouteOnMap(this.selectedCenter,t)}calculateRouteInfo(t){return m(this,null,function*(){if(!this.userLocation)return;let n=["walking","cycling","driving"];for(let e of n)try{let o=this.mapboxRouting.convertTravelModeToProfile(e),r=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),o);if(r.routes&&r.routes.length>0){let d=r.routes[0];this.routeInfo[e]={duration:d.duration,distance:d.distance},console.log(`\u{1F3D4}\uFE0F LANDSLIDE: ${e} route calculated - ${(d.distance/1e3).toFixed(2)}km, ${Math.round(d.duration/60)}min`)}}catch(o){console.error(`\u{1F3D4}\uFE0F LANDSLIDE: Error calculating ${e} route:`,o);let r=this.calculateDistance(this.userLocation.lat,this.userLocation.lng,Number(t.latitude),Number(t.longitude));this.routeInfo[e]={duration:r/(e==="walking"?5e3:e==="cycling"?15e3:5e4)*3600,distance:r}}})}showRouteOnMap(t,n){return m(this,null,function*(){if(this.userLocation)try{let e=this.mapboxRouting.convertTravelModeToProfile(n),o=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),e);if(o.routes&&o.routes.length>0){let r=o.routes[0],d=this.mapboxRouting.convertToGeoJSON(r);this.clearRoutes(),this.routeLayer||(this.routeLayer=c.layerGroup().addTo(this.map));let p=c.geoJSON(d,{style:{color:"#8B4513",weight:4,opacity:.8}});p.isRouteLayer=!0,p.addTo(this.routeLayer)}}catch(e){console.error("Error showing route on map:",e)}})}formatTime(t){return t?`${Math.round(t/60)} min`:"--"}formatDistance(t){if(!t)return"--";let n=t/1e3;return n<1?`${Math.round(t)} m`:`${n.toFixed(1)} km`}ionViewWillLeave(){this.clearRoutes(),this.isRealTimeNavigationActive&&this.osmRouting.stopRealTimeRouting(),this.map&&this.map.remove()}routeToCenter(t,n){return m(this,null,function*(){if(this.userLocation)try{this.clearRoutes();let e=Number(t.latitude),o=Number(t.longitude);if(!isNaN(e)&&!isNaN(o)){console.log(`\u{1F3D4}\uFE0F LANDSLIDE: Creating Mapbox route to ${t.name} via ${n}`);let r=this.mapboxRouting.convertTravelModeToProfile(n),d=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,o,e,r);if(d&&d.routes&&d.routes.length>0){let p=d.routes[0],f="#8b5a2b";this.routeLayer=c.layerGroup().addTo(this.map);let O=c.polyline(p.geometry.coordinates.map(T=>[T[1],T[0]]),{color:f,weight:5,opacity:.8});O.isRouteLayer=!0,O.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`\u{1F3D4}\uFE0F Route: ${(p.distance/1e3).toFixed(2)}km, ${(p.duration/60).toFixed(0)}min via ${n}`,duration:4e3,color:"warning"})).present(),this.map.fitBounds(O.getBounds(),{padding:[50,50]}),console.log(`\u2705 LANDSLIDE: Successfully created route with ${p.geometry.coordinates.length} points`)}}}catch(e){console.error("\u{1F3D4}\uFE0F Error routing to landslide center:",e),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}startRealTimeNavigation(t){return m(this,null,function*(){if(console.log("\u{1F9ED} Starting real-time navigation to landslide center:",t.name),!this.selectedTransportMode){console.error("\u274C No transport mode selected");return}yield this.routeToCenter(t,this.selectedTransportMode),this.navigationDestination={lat:Number(t.latitude),lng:Number(t.longitude),name:t.name},this.isRealTimeNavigationActive=!0,this.closeNavigationPanel(),this.toastCtrl.create({message:`\u{1F9ED} Real-time navigation started to ${t.name} via ${this.selectedTransportMode}`,duration:3e3,color:"primary"}).then(n=>n.present()),console.log("\u2705 Landslide map real-time navigation setup complete")})}onNavigationRouteUpdated(t){console.log("\u{1F504} Landslide map navigation route updated"),this.currentNavigationRoute=t,this.updateMapWithNavigationRoute(t)}onNavigationStopped(){console.log("\u23F9\uFE0F Landslide map real-time navigation stopped"),this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.clearNavigationRoute(),this.toastCtrl.create({message:"\u23F9\uFE0F Navigation stopped",duration:2e3,color:"medium"}).then(t=>t.present())}updateMapWithNavigationRoute(t){if(this.clearNavigationRoute(),t.geometry&&t.geometry.coordinates){let n=this.osmRouting.convertToGeoJSON(t),e=c.geoJSON(n,{style:{color:"#8b4513",weight:6,opacity:.8,dashArray:"10, 5"}}).addTo(this.map);e.isNavigationRoute=!0}}clearNavigationRoute(){this.map.eachLayer(t=>{t.isNavigationRoute&&this.map.removeLayer(t)})}static{this.\u0275fac=function(n){return new(n||l)}}static{this.\u0275cmp=E({type:l,selectors:[["app-landslide-map"]],standalone:!0,features:[z],decls:64,vars:21,consts:[[3,"translucent"],["color","tertiary"],["slot","start"],["fill","clear",3,"click"],["name","arrow-back-outline","color","light"],[3,"fullscreen"],["id","landslide-map",2,"height","100%","width","100%"],[1,"map-controls"],["fill","clear",1,"control-btn",3,"click"],["src","assets/home-insuranceForLandslide.png","alt","All Centers",1,"control-icon"],["src","assets/downloadForLandslide.png","alt","Download",1,"control-icon"],["src","assets/compassForLandslide.png","alt","Route to Nearest",1,"control-icon"],[1,"travel-mode-selector"],[3,"ngModelChange","ionChange","ngModel"],["value","walking"],["name","walk-outline"],["value","cycling"],["name","bicycle-outline"],["value","driving"],["name","car-outline"],["class","route-info",4,"ngIf"],[1,"all-centers-panel"],[1,"panel-content"],[1,"panel-header"],[1,"header-info"],["name","close"],[1,"centers-list"],["class","center-item",3,"click",4,"ngFor","ngForOf"],[1,"all-centers-overlay",3,"click"],[3,"destination","travelMode","autoStart","routeUpdated","navigationStopped",4,"ngIf"],[1,"navigation-panel"],[4,"ngIf"],["class","transport-options",4,"ngIf"],[1,"route-footer"],[1,"footer-content"],[1,"route-summary"],[1,"transport-icon"],[3,"name"],[1,"route-details"],[1,"destination"],["class","route-info-footer",4,"ngIf"],["fill","solid","color","tertiary",3,"click"],[1,"route-info"],[1,"route-header"],["name","time-outline","color","tertiary"],[1,"route-item"],["name","location-outline"],[1,"center-item",3,"click"],[1,"center-info"],[1,"address"],[1,"center-details"],["class","distance",4,"ngIf"],[1,"capacity"],[1,"center-actions"],["name","chevron-forward-outline"],[1,"distance"],[3,"routeUpdated","navigationStopped","destination","travelMode","autoStart"],[1,"transport-options"],[1,"option-header"],["name","navigate-outline"],[1,"transport-buttons"],[1,"transport-btn",3,"click"],["class","start-navigation-btn",3,"click",4,"ngIf"],[1,"time"],[1,"start-navigation-btn",3,"click"],[1,"route-info-footer"]],template:function(n,e){n&1&&(i(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),C("click",function(){return e.goBack()}),u(4,"ion-icon",4),a()()()(),i(5,"ion-content",5),u(6,"div",6),i(7,"div",7)(8,"ion-button",8),C("click",function(){return e.showAllCenters()}),u(9,"img",9),a(),i(10,"ion-button",8),C("click",function(){return e.downloadMap()}),u(11,"img",10),a(),i(12,"ion-button",8),C("click",function(){return e.routeToNearestCenters()}),u(13,"img",11),a()(),i(14,"div",12)(15,"ion-segment",13),$("ngModelChange",function(r){return R(e.travelMode,r)||(e.travelMode=r),r}),C("ionChange",function(r){return e.onTravelModeChange(r)}),i(16,"ion-segment-button",14),u(17,"ion-icon",15),i(18,"ion-label"),g(19,"Walk"),a()(),i(20,"ion-segment-button",16),u(21,"ion-icon",17),i(22,"ion-label"),g(23,"Cycle"),a()(),i(24,"ion-segment-button",18),u(25,"ion-icon",19),i(26,"ion-label"),g(27,"Drive"),a()()()(),v(28,Ct,16,3,"div",20),i(29,"div",21)(30,"div",22)(31,"div",23)(32,"div",24)(33,"h3"),g(34,"\u{1F3D4}\uFE0F Landslide Evacuation Centers"),a(),i(35,"p"),g(36),a()(),i(37,"ion-button",3),C("click",function(){return e.closeAllCentersPanel()}),u(38,"ion-icon",25),a()(),i(39,"div",26),v(40,vt,12,4,"div",27),a()()(),i(41,"div",28),C("click",function(){return e.closeAllCentersPanel()}),a(),v(42,Pt,1,3,"app-real-time-navigation",29),i(43,"div",30)(44,"div",22)(45,"div",23)(46,"div",24)(47,"h3"),g(48),a(),v(49,xt,2,1,"p",31),a(),i(50,"ion-button",3),C("click",function(){return e.closeNavigationPanel()}),u(51,"ion-icon",25),a()(),v(52,Lt,22,10,"div",32),a()(),i(53,"div",33)(54,"div",34)(55,"div",35)(56,"div",36),u(57,"ion-icon",37),a(),i(58,"div",38)(59,"div",39),g(60),a(),v(61,Nt,5,2,"div",40),a()(),i(62,"ion-button",41),C("click",function(){return e.startRealTimeNavigation(e.selectedCenter)}),g(63," Start "),a()()()()),n&2&&(_("translucent",!0),s(5),_("fullscreen",!0),s(10),A("ngModel",e.travelMode),s(13),_("ngIf",e.routeTime>0&&e.routeDistance>0),s(),w("show",e.showAllCentersPanel),s(7),L("",e.evacuationCenters.length," centers available"),s(4),_("ngForOf",e.evacuationCenters),s(),w("show",e.showAllCentersPanel),s(),_("ngIf",e.navigationDestination),s(),w("show",e.selectedCenter),s(5),L("\u{1F3D4}\uFE0F ",e.selectedCenter==null?null:e.selectedCenter.name,""),s(),_("ngIf",e.selectedCenter),s(3),_("ngIf",e.selectedCenter),s(),w("show",e.showRouteFooter&&e.selectedCenter),s(4),_("name",e.selectedTransportMode==="walking"?"walk-outline":e.selectedTransportMode==="cycling"?"bicycle-outline":"car-outline"),s(3),M(e.selectedCenter==null?null:e.selectedCenter.name),s(),_("ngIf",e.routeInfo[e.selectedTransportMode]))},dependencies:[ct,H,Y,Q,X,Z,tt,et,nt,ot,it,at,q,B,F,V,K,j,J,ht],styles:["#landslide-map[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.map-controls[_ngcontent-%COMP%]{position:absolute;top:80px;right:10px;z-index:1000;display:flex;flex-direction:column;gap:8px}.control-btn[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .95);--color: #8B4513;--border-radius: 12px;width:50px;height:50px;box-shadow:0 4px 12px #00000026;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(139,69,19,.2)}.control-btn[_ngcontent-%COMP%]:hover{--background: rgba(139, 69, 19, .1);transform:translateY(-2px);box-shadow:0 6px 16px #0003}.control-icon[_ngcontent-%COMP%]{width:28px;height:28px;object-fit:contain}.all-centers-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-400px;width:400px;height:100vh;background:#fff;box-shadow:-4px 0 20px #00000026;z-index:2000;transition:right .3s ease-in-out;overflow:hidden}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.all-centers-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:20px}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20px;padding-bottom:16px;border-bottom:2px solid var(--ion-color-tertiary-tint)}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:20px;font-weight:700;color:var(--ion-color-tertiary);line-height:1.2}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding-right:4px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;margin-bottom:12px;background:#fff;border-radius:12px;border:1px solid var(--ion-color-light);cursor:pointer;transition:all .2s ease}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-tertiary-tint);background:var(--ion-color-tertiary-tint);transform:translateY(-2px)}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 4px;font-size:16px;font-weight:600;color:var(--ion-color-dark)}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%]{margin:0 0 8px;font-size:13px;color:var(--ion-color-medium)}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]{display:flex;gap:12px;flex-wrap:wrap}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%], .all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .capacity[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-tertiary);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-actions[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:18px}.all-centers-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#00000080;z-index:1500;opacity:0;visibility:hidden;transition:all .3s ease}.all-centers-overlay.show[_ngcontent-%COMP%]{opacity:1;visibility:visible}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: #8B4513;--color: white;--border-width: 0}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;font-size:18px}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.navigation-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-400px;width:400px;height:100vh;background:#fff;box-shadow:-4px 0 20px #00000026;z-index:2000;transition:right .3s ease-in-out;overflow:hidden}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:20px}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20px;padding-bottom:16px;border-bottom:2px solid var(--ion-color-tertiary-tint)}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:20px;font-weight:700;color:var(--ion-color-tertiary);line-height:1.2}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);font-weight:500}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px;font-weight:600;color:var(--ion-color-tertiary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin-bottom:20px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;border:2px solid var(--ion-color-light);border-radius:12px;background:#fff;cursor:pointer;transition:all .2s ease;position:relative}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-tertiary-tint);background:var(--ion-color-tertiary-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]{border-color:var(--ion-color-tertiary);background:var(--ion-color-tertiary-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-tertiary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-right:12px;color:var(--ion-color-medium);transition:color .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:var(--ion-color-dark);flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;gap:2px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-tertiary)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]{width:100%;padding:16px;background:var(--ion-color-tertiary);color:#fff;border:none;border-radius:12px;font-size:16px;font-weight:600;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:background .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]:hover{background:var(--ion-color-tertiary-shade)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.route-footer[_ngcontent-%COMP%]{position:fixed;bottom:-100px;left:0;right:0;background:#fff;border-top:1px solid var(--ion-color-light);box-shadow:0 -4px 20px #0000001a;z-index:1500;transition:bottom .3s ease-in-out}.route-footer.show[_ngcontent-%COMP%]{bottom:0}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{padding:16px 20px;display:flex;align-items:center;justify-content:space-between;gap:16px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;flex:1}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:var(--ion-color-tertiary-tint);display:flex;align-items:center;justify-content:center}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:var(--ion-color-tertiary)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{flex:1}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .destination[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-dark);margin-bottom:2px;line-height:1.2}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]{display:flex;gap:8px;align-items:center}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:var(--ion-color-tertiary)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 20px;--padding-start: 20px;--padding-end: 20px;font-weight:600}.panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:20px;border-bottom:1px solid #eee;background:#8b4513;color:#fff}.panel-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:1.2rem;font-weight:600}.panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: white}.panel-content[_ngcontent-%COMP%]{padding:20px}.panel-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:10px 0;color:#333}.transport-options[_ngcontent-%COMP%]{margin-top:20px}.transport-options[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 15px;color:#8b4513;font-size:1rem;font-weight:600}.transport-option[_ngcontent-%COMP%]{display:flex;align-items:center;padding:15px;margin:10px 0;border:2px solid #eee;border-radius:12px;cursor:pointer;transition:all .3s ease;background:#fff}.transport-option[_ngcontent-%COMP%]:hover{border-color:#8b4513;background:#f9f7f4}.transport-option.selected[_ngcontent-%COMP%]{border-color:#8b4513;background:#8b4513;color:#fff}.transport-option.selected[_ngcontent-%COMP%]   .transport-info[_ngcontent-%COMP%]{color:#fff}.transport-option.selected[_ngcontent-%COMP%]   .transport-info[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]{color:#ffffffe6}.transport-option[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-right:15px;color:#8b4513}.transport-option.selected[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#fff}.transport-info[_ngcontent-%COMP%]{flex:1}.transport-info[_ngcontent-%COMP%]   .mode[_ngcontent-%COMP%]{display:block;font-weight:600;font-size:1rem;margin-bottom:2px}.transport-info[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]{display:block;font-size:.85rem;color:#666}.leaflet-container[_ngcontent-%COMP%]{height:100%;width:100%}.leaflet-popup-content[_ngcontent-%COMP%]{margin:8px 12px;line-height:1.4}.evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 10px;color:#8b4513;font-size:1.1rem}.evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:5px 0;font-size:.9rem}.evacuation-popup[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#333}.travel-mode-selector[_ngcontent-%COMP%]{position:absolute;top:20px;left:20px;z-index:1000;background:#fffffff2;border-radius:12px;padding:8px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]{--background: transparent;min-height:40px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);--color-checked: var(--ion-color-tertiary);--indicator-color: var(--ion-color-tertiary);min-height:40px}.travel-mode-selector[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;margin-bottom:2px}.travel-mode-selector[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-info[_ngcontent-%COMP%]{position:absolute;bottom:20px;left:20px;z-index:1000;max-width:200px}.route-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;border-radius:12px;background:#fffffff2}.route-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-tertiary);margin-bottom:8px}.route-info[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:14px;color:var(--ion-color-dark)}.route-info[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;color:var(--ion-color-tertiary)}@media (max-width: 768px){.map-controls[_ngcontent-%COMP%]{top:70px;right:8px}.all-centers-panel[_ngcontent-%COMP%], .navigation-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.all-centers-panel.show[_ngcontent-%COMP%], .navigation-panel.show[_ngcontent-%COMP%]{right:0}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{padding:12px 16px}}"]})}}return l})();export{Kt as LandslideMapPage};
